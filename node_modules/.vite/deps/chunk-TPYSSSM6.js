var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);

// node_modules/svelte/src/shared/version.js
var VERSION = "4.2.20";
var PUBLIC_VERSION = "4";

export {
  __publicField,
  VERSION,
  PUBLIC_VERSION
};
//# sourceMappingURL=chunk-TPYSSSM6.js.map
